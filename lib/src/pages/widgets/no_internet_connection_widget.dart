import 'package:wasla/src/core/config/app_config.dart';
import 'package:wasla/src/core/utils/color_manager.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import '../web_view_page.dart';

class NoInternetConnectionWidget extends StatelessWidget {
  const NoInternetConnectionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Center(
          child: Icon(CupertinoIcons.wifi_slash, size: 100),
        ),
        const SizedBox(height: 40),
        Text(
          "No Internet Connection !",
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        const SizedBox(height: 40),
        SizedBox(
          height: 45,
          width: 250,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              // primary: Theme.of(context).primaryColor,
              // onPrimary: Colors.white,
              backgroundColor: ColorManager.primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onPressed: () {
              context.read<AppConfig>().init();
              context.toReplacement(const WebViewPage());
            },
            child: const Text("Try Again",
                style: TextStyle(
                  color: Colors.white,
                )),
          ),
        )
      ],
    );
  }
}
