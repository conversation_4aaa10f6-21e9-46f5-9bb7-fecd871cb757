import 'package:wasla/src/core/utils/app_constants.dart';
import 'package:wasla/src/core/utils/loading_widget.dart';
import 'package:wasla/src/pages/widgets/no_internet_connection_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:provider/provider.dart';

import '../core/config/app_config.dart';

class WebViewPage extends StatefulWidget {
  const WebViewPage({super.key});

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  @override
  Widget build(BuildContext context) {
    return Consumer<AppConfig>(
      builder: (context, appConfig, child) {
        return WillPopScope(
          onWillPop: () async {
            if (appConfig.webViewController != null) {
              final canGoBack = await appConfig.webViewController!.canGoBack();
              if (canGoBack) {
                appConfig.webViewController!.goBack();
                return false;
              }
            }
            return false;
          },
          child: Scaffold(
            backgroundColor: Colors.white,
            body: SafeArea(
              child: Builder(builder: (context) {
                if (!appConfig.hasInternet) {
                  return const NoInternetConnectionWidget();
                }

                if (appConfig.isLoading) {
                  return const Center(
                    child: LoadingWidget(),
                  );
                }

                // final token = OneSignalNotificationService.getUserId();

                return InAppWebView(
                  onWebViewCreated: appConfig.onWebViewCreated,
                  // onLoadStop: (controller, url) async {
                  //   await appConfig.addTokenToLogin(controller: controller);
                  // },
                  // onProgressChanged: (controller, progress) async {
                  //   if (progress == 100) {
                  //     await appConfig.addTokenToLogin(controller: controller);
                  //   }
                  // },
                  // onUpdateVisitedHistory:
                  //     (controller, url, androidIsReload) async {
                  //   await appConfig.addTokenToLogin(controller: controller);
                  // },
                  onReceivedServerTrustAuthRequest:
                      (controller, challenge) async {
                    return ServerTrustAuthResponse(
                        action: ServerTrustAuthResponseAction.PROCEED);
                  },
                  initialUrlRequest: URLRequest(
                    url: WebUri.uri(
                      Uri.parse(AppConstants.appUrl),
                    ),
                  ),
                );
              }),
            ),
          ),
        );
      },
    );
  }
}
