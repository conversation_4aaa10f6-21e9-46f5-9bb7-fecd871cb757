import 'package:wasla/src/core/utils/color_manager.dart';
import 'package:flutter/material.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

class LoadingWidget extends StatelessWidget {
  final bool isLinear;
  final Color color;

  const LoadingWidget(
      {super.key,
      this.isLinear = false,
      this.color = ColorManager.primaryColor});

  @override
  Widget build(BuildContext context) {
    if (isLinear) {
      return LinearProgressIndicator(
        backgroundColor: color.withOpacity(0.2),
        valueColor: AlwaysStoppedAnimation<Color>(color),
      );
    }
    return Center(
      child: LoadingAnimationWidget.discreteCircle(color: color, size: 45),
    );
  }
}
