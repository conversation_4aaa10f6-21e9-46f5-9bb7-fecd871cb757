import 'package:wasla/src/core/config/app_config.dart';
import 'package:wasla/src/core/utils/app_constants.dart';
import 'package:wasla/src/pages/web_view_page.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (BuildContext context) => AppConfig()..init(),
      child: MaterialApp(
        title: AppConstants.appName,
        theme: ThemeData(
          primarySwatch: Colors.blue,
        ),
        debugShowCheckedModeBanner: false,
        debugShowMaterialGrid: false,
        home: const WebViewPage(),
      ),
    );
  }
}
